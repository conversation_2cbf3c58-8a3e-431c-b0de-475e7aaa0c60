# File Service - AWS S3 Implementation

This module provides file upload, download, and management functionality using AWS S3 as the storage backend.

## Files

- `awsservice.ts` - Core AWS S3 service implementation
- `file.service.ts` - Main file service that uses AWS S3 service
- `file.controller.ts` - REST API endpoints for file operations
- `file.module.ts` - NestJS module configuration

## Environment Variables

Add these environment variables to your `.env` file:

```env
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_S3_BUCKET_NAME=your-s3-bucket-name
```

## API Endpoints

### Upload File
- **POST** `/v1/file/s3upload`
- Upload a file to S3
- Returns file information including signed URL

### Get File
- **GET** `/v1/file/:filename`
- Download a file from S3
- Streams the file directly to the response

### Get File URL
- **GET** `/v1/file/url/:filename`
- Get a signed URL for a file (1 year expiration)
- Returns: `{ url: "signed_url" }`

### Delete File
- **DELETE** `/v1/file/:filename`
- Delete a file from S3
- Returns: `{ success: boolean, message: string }`

### Health Check
- **GET** `/v1/file/health/s3`
- Check S3 connection status
- Returns: `{ status: "healthy|unhealthy", service: "AWS S3", timestamp: "ISO_date" }`

## Features

- **Audio Conversion**: Automatically converts audio files to WAV format using ffmpeg
- **Unique Filenames**: Generates unique filenames to prevent conflicts
- **Signed URLs**: Provides secure, time-limited access to files
- **Error Handling**: Comprehensive error handling and logging
- **Legacy Support**: Maintains backward compatibility with GCS method names

## Usage Example

```typescript
import { FileService } from './file.service';

// Upload a file
const fileInfo = await fileService.s3Upload(file);

// Get a signed URL
const url = await fileService.getS3FilePublicUrl(filename);

// Delete a file
const success = await fileService.deleteFromS3(filename);
```

## Dependencies

- `@aws-sdk/client-s3` - AWS S3 client
- `@aws-sdk/s3-request-presigner` - For generating signed URLs
- `fluent-ffmpeg` - For audio file conversion

## Migration from GCS

The service maintains backward compatibility with the previous GCS implementation. Legacy method names (`gcsUpload`, `getGCSFilePublicUrl`, `getFromGCS`) are still available and will redirect to the corresponding S3 methods.
