/* eslint-disable prettier/prettier */
/*  
https://docs.nestjs.com/controllers#controllers
*/

import {
    Controller,
    Get,
    Param,
    Post,
    Req,
    Res,
    UploadedFile,
    UseInterceptors
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { Request, Response } from 'express';
import { diskStorage } from 'multer';
import { FileService } from './file.service';
import { ApiTags } from '@nestjs/swagger';
import { LogService } from 'src/log/log.service';

@ApiTags('File V1')
@Controller('v1/file')
export class FileController {
    constructor(private fileService: FileService, private logService: LogService) { }
    @Post()
    @UseInterceptors(FileInterceptor('file', {
        storage: diskStorage({
            destination: './uploads',
            filename: function (req, file, cb) {
                const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
                this.logService.log('unique: ', uniqueSuffix);
                const parts = file.originalname?.split('.');
                const ext = parts[parts.length - 1];
                cb(null, file.fieldname + '-' + uniqueSuffix + '.' + ext);
            },
        })
    }))

    uploadFile(@UploadedFile() file: Express.Multer.File, @Req() req: Request) {
        // this.logService.log('file upload route: ', file);
        const response = {
            originalname: file.originalname,
            filename: file.filename,
            url: req.url + '/' + file.filename,
            type: file.mimetype,
            size: file.size,
            message: file.originalname + ' uploaded successfully'
        };
        this.logService.log('response: ', response);

        return response;
        // return this.fileService.upload(req, res);
    }
    @Get(':id')
    async getFile(@Param('id') filename: string, @Res() res: Response) {
        this.logService.log('file Key: ', filename);
        return this.fileService.getFromGCS(filename, res)
        // return { url: await this.fileService.getFromGCS(key) }
        // @Param('id') key: string,

        // return res.sendFile(id, { root: 'uploads' });
    }
    @Post('s3upload')
    @UseInterceptors(FileInterceptor('file', {}))
    s3Upload(@UploadedFile() file: Express.Multer.File, @Req() req: Request, res: Response) {
        this.logService.log('file: ', file)
        return this.fileService.gcsUpload(file)
    }

}
