<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Chat WebSocket Test Client</title>
    <script
      src="https://cdn.socket.io/4.7.2/socket.io.min.js"
      integrity="sha384-mZLF4UVrpi/QTWPA7BjNPEnkIfRFn4ZEO3Qt/HFklTJBj/gBOV8G3HcKn4NfQblz"
      crossorigin="anonymous"
    ></script>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
          sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px;
      }

      .main-container {
        max-width: 1400px;
        margin: 0 auto;
        background: white;
        border-radius: 12px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }

      .header {
        background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
        color: white;
        padding: 20px 30px;
        text-align: center;
      }

      .header h1 {
        font-size: 2.5rem;
        margin-bottom: 10px;
        font-weight: 300;
      }

      .header p {
        opacity: 0.9;
        font-size: 1.1rem;
      }

      .content {
        display: grid;
        grid-template-columns: 1fr 2fr;
        gap: 0;
        height: calc(100vh - 200px);
      }

      .sidebar {
        background: #f8f9fa;
        border-right: 1px solid #e9ecef;
        overflow-y: auto;
        padding: 20px;
      }

      .main-content {
        padding: 20px;
        overflow-y: auto;
      }

      .section {
        margin-bottom: 30px;
        background: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
      }

      .section h2 {
        color: #2c3e50;
        margin-bottom: 15px;
        font-size: 1.4rem;
        font-weight: 600;
        border-bottom: 2px solid #3498db;
        padding-bottom: 8px;
      }

      .connection-status {
        display: inline-block;
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.9rem;
        margin-bottom: 15px;
        transition: all 0.3s ease;
      }

      .connected {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
        animation: pulse 2s infinite;
      }

      .disconnected {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
      }

      @keyframes pulse {
        0%,
        100% {
          opacity: 1;
        }
        50% {
          opacity: 0.7;
        }
      }

      .form-group {
        margin-bottom: 15px;
      }

      .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
        margin-bottom: 15px;
      }

      label {
        display: block;
        margin-bottom: 5px;
        font-weight: 600;
        color: #2c3e50;
        font-size: 0.9rem;
      }

      input,
      select,
      textarea {
        width: 100%;
        padding: 12px;
        border: 2px solid #e9ecef;
        border-radius: 6px;
        font-size: 14px;
        transition: border-color 0.3s ease;
        font-family: inherit;
      }

      input:focus,
      select:focus,
      textarea:focus {
        outline: none;
        border-color: #3498db;
        box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
      }

      .btn {
        background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
        color: white;
        padding: 12px 24px;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 600;
        margin-right: 10px;
        margin-bottom: 10px;
        transition: all 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
      }

      .btn:active {
        transform: translateY(0);
      }

      .btn-success {
        background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
      }

      .btn-success:hover {
        box-shadow: 0 4px 12px rgba(46, 204, 113, 0.3);
      }

      .btn-danger {
        background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
      }

      .btn-danger:hover {
        box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
      }

      .btn-warning {
        background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
      }

      .btn:disabled {
        background: #95a5a6;
        cursor: not-allowed;
        transform: none;
      }

      .btn-group {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-top: 15px;
      }

      .log-container {
        background: #1a1a1a;
        border-radius: 8px;
        padding: 20px;
        height: 400px;
        overflow-y: auto;
        font-family: 'Courier New', monospace;
        font-size: 13px;
        color: #e0e0e0;
        border: 1px solid #333;
      }

      .log-entry {
        margin-bottom: 8px;
        padding: 8px 12px;
        border-radius: 4px;
        border-left: 4px solid;
        animation: fadeIn 0.3s ease;
      }

      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .log-sent {
        background: rgba(52, 152, 219, 0.1);
        border-left-color: #3498db;
      }

      .log-received {
        background: rgba(155, 89, 182, 0.1);
        border-left-color: #9b59b6;
      }

      .log-info {
        background: rgba(241, 196, 15, 0.1);
        border-left-color: #f1c40f;
      }

      .log-error {
        background: rgba(231, 76, 60, 0.1);
        border-left-color: #e74c3c;
      }

      .log-success {
        background: rgba(46, 204, 113, 0.1);
        border-left-color: #2ecc71;
      }

      .timestamp {
        color: #95a5a6;
        font-size: 11px;
        margin-right: 8px;
      }

      .log-content {
        margin-top: 5px;
        padding-left: 10px;
        border-left: 2px solid #34495e;
      }

      .quick-actions {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-top: 20px;
      }

      .action-card {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 15px;
        transition: all 0.3s ease;
      }

      .action-card:hover {
        border-color: #3498db;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      .action-card h3 {
        color: #2c3e50;
        margin-bottom: 10px;
        font-size: 1.1rem;
      }

      .stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
        margin-bottom: 20px;
      }

      .stat-card {
        background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
        color: white;
        padding: 15px;
        border-radius: 8px;
        text-align: center;
      }

      .stat-number {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 5px;
      }

      .stat-label {
        font-size: 0.9rem;
        opacity: 0.9;
      }

      .toggle-switch {
        position: relative;
        width: 60px;
        height: 30px;
        background: #ccc;
        border-radius: 15px;
        cursor: pointer;
        transition: background 0.3s ease;
      }

      .toggle-switch.active {
        background: #3498db;
      }

      .toggle-switch::after {
        content: '';
        position: absolute;
        top: 3px;
        left: 3px;
        width: 24px;
        height: 24px;
        background: white;
        border-radius: 12px;
        transition: left 0.3s ease;
      }

      .toggle-switch.active::after {
        left: 33px;
      }

      .json-editor {
        background: #2c3e50;
        color: #ecf0f1;
        font-family: 'Courier New', monospace;
        border: 1px solid #34495e;
        border-radius: 6px;
        padding: 15px;
        min-height: 120px;
        resize: vertical;
      }

      .json-editor:focus {
        border-color: #3498db;
        box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
      }

      .scrollbar::-webkit-scrollbar {
        width: 8px;
      }

      .scrollbar::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
      }

      .scrollbar::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 4px;
      }

      .scrollbar::-webkit-scrollbar-thumb:hover {
        background: #555;
      }

      @media (max-width: 768px) {
        .content {
          grid-template-columns: 1fr;
        }

        .form-row {
          grid-template-columns: 1fr;
        }

        .btn-group {
          flex-direction: column;
        }
      }
    </style>
  </head>

  <body>
    <div class="main-container">
      <div class="header">
        <h1>🚀 Multi-Gateway WebSocket Test Client</h1>
        <p>Real-time testing interface for Chat, Messages, Live Chat, and Events gateways</p>
      </div>

      <div class="content">
        <div class="sidebar scrollbar">
          <!-- Connection Section -->
          <div class="section">
            <h2>🔗 Connection</h2>
            <div id="connectionStatus" class="connection-status disconnected">
              ⚫ Disconnected
            </div>

            <div class="form-row">
              <div class="form-group">
                <label>Server URL</label>
                <input
                  id="serverUrl"
                  type="text"
                  value="http://localhost:9000"
                  placeholder="http://localhost:9000"
                />
              </div>
              <div class="form-group">
                <label>Gateway Type</label>
                <select id="gatewayType" onchange="updateEventDataTemplate()">
                  <option value="chat">Chat Gateway (chat-event)</option>
                  <option value="messages">Messages Gateway (message)</option>
                  <option value="live-chat">Live Chat Gateway (live-chat-event)</option>
                  <option value="events">Events Gateway (event)</option>
                </select>
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label>User ID</label>
                <input
                  id="userId"
                  type="text"
                  value="ASWHY67HDBVWT5TGDHSJK"
                  placeholder="User ID"
                />
              </div>
              <div class="form-group">
                <label>Tenant ID</label>
                <input
                  id="tenantId"
                  type="text"
                  value="a9a851ea-d637-474a-8439-e5430f3d3b17"
                  placeholder="Tenant ID"
                />
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label>JobPro User ID</label>
                <input
                  id="jobProUserId"
                  type="text"
                  value="c964a01d-d0f3-4553-b53d-07e773d66525"
                  placeholder="JobPro User ID"
                />
              </div>
              <div class="form-group">
                <label>Email</label>
                <input
                  id="email"
                  type="email"
                  value="<EMAIL>"
                  placeholder="Email address"
                />
              </div>
            </div>

            <div class="form-group">
              <label>Auth Token</label>
              <input
                id="authToken"
                type="password"
                value="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.V3wtVpqcp0A1msOYq5bHcrNQd-A24gl1UddCTtG0vq0"
                placeholder="JWT token for authentication"
              />
            </div>

            <div class="btn-group">
              <button class="btn btn-success" onclick="connect()">
                🔌 Connect
              </button>
              <button class="btn btn-danger" onclick="disconnect()">
                🔌 Disconnect
              </button>
            </div>
          </div>
            </div>
          </div>

          <!-- Quick Test Actions -->
          <div class="section">
            <h2>⚡ Quick Tests</h2>
            <div class="btn-group">
              <button class="btn" onclick="getUserData()">
                👤 Get User Data
              </button>
              <button class="btn" onclick="getChats()">💬 Get Chats</button>
              <button class="btn" onclick="createTestChat()">
                ➕ Create Test Chat
              </button>
              <button class="btn" onclick="sendTestMessage()">
                📤 Send Test Message
              </button>
              <button class="btn" onclick="startTypingTest()">
                ⌨️ Start Typing
              </button>
              <button class="btn" onclick="testCall()">📞 Test Call</button>
            </div>
          </div>

          <!-- Chat Context -->
          <div class="section">
            <h2>💬 Chat Context</h2>
            <div class="form-group">
              <label>Current Chat ID</label>
              <input
                id="chatId"
                type="text"
                placeholder="Enter or select chat ID"
              />
            </div>
            <div class="form-group">
              <label>Message Text</label>
              <textarea
                id="messageText"
                rows="3"
                placeholder="Type your test message here..."
              ></textarea>
            </div>
            <div class="form-group">
              <label>Message ID</label>
              <input
                id="messageId"
                type="text"
                placeholder="Message ID for operations"
              />
            </div>
          </div>

          <!-- Event Statistics -->
          <div class="section">
            <h2>📊 Statistics</h2>
            <div class="stats">
              <div class="stat-card">
                <div class="stat-number" id="sentCount">0</div>
                <div class="stat-label">Sent</div>
              </div>
              <div class="stat-card">
                <div class="stat-number" id="receivedCount">0</div>
                <div class="stat-label">Received</div>
              </div>
              <div class="stat-card">
                <div class="stat-number" id="errorCount">0</div>
                <div class="stat-label">Errors</div>
              </div>
            </div>
          </div>
        </div>

        <div class="main-content scrollbar">
          <!-- Event Log -->
          <div class="section">
            <h2>📋 Event Log</h2>
            <div class="btn-group">
              <button class="btn btn-danger" onclick="clearLog()">
                🗑️ Clear Log
              </button>
              <button class="btn btn-warning" onclick="exportLog()">
                📥 Export Log
              </button>
              <button class="btn" onclick="toggleAutoScroll()">
                <span id="autoScrollText">🔄 Auto-scroll: ON</span>
              </button>
            </div>
            <div id="logContainer" class="log-container scrollbar"></div>
          </div>

          <!-- Custom Event Section -->
          <div class="section">
            <h2>🎯 Custom Event Builder</h2>
            <div
              style="
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                padding: 20px;
                border-radius: 10px;
                color: white;
                margin-bottom: 20px;
              "
            >
              <h3 style="margin: 0 0 10px 0; font-size: 1.3rem">
                🚀 Build & Send Custom Events
              </h3>
              <p style="margin: 0; opacity: 0.9">
                Create and send custom WebSocket events with full control over
                event type and payload data. Works with all gateway types.
              </p>
            </div>

            <div
              style="
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 20px;
                margin-bottom: 20px;
              "
            >
              <div>
                <label style="font-size: 1.1rem; margin-bottom: 10px"
                  >Event Type</label
                >
                <select
                  id="eventType"
                  style="font-size: 16px; padding: 15px; height: auto"
                >
                  <!-- Chat Gateway Events -->
                  <option value="GET_USER_DATA">👤 Get User Data</option>
                  <option value="GET_CHATS_WITH_UNREAD_COUNT">
                    💬 Get Chats with Unread Count
                  </option>
                  <option value="GET_CHAT_PAGINATED_MESSAGES">
                    📄 Get Chat Messages (Paginated)
                  </option>
                  <option value="CREATE_CHAT">➕ Create Chat</option>
                  <option value="NEW_MESSAGE">📝 New Message</option>
                  <option value="UPDATE_MESSAGE">✏️ Update Message</option>
                  <option value="DELETE_MESSAGE">🗑️ Delete Message</option>
                  <option value="PIN_UNPIN_MESSAGE">
                    📌 Pin/Unpin Message
                  </option>
                  <option value="ADD_REACTION_TO_MESSAGE">
                    😀 Add Reaction
                  </option>
                  <option value="REMOVE_REACTION_FROM_MESSAGE">
                    ❌ Remove Reaction
                  </option>
                  <option value="IS_TYPING">⌨️ Is Typing</option>
                  <option value="IS_RECORDING">🎙️ Is Recording</option>
                  <option value="UPDATE_READ_STATUS">
                    ✅ Update Read Status
                  </option>
                  <option value="GET_ALL_UNREAD_MESSAGES">
                    📬 Get All Unread Messages
                  </option>
                  <option value="MARK_ALL_AS_READ">
                    ✅ Mark All as Read
                  </option>
                  <option value="UPDATE_CHAT">✏️ Update Chat</option>
                  <option value="ADD_USERS_TO_CHAT">
                    👥 Add Users to Chat
                  </option>
                  <option value="REMOVE_USER_FROM_CHAT">
                    🚫 Remove User from Chat
                  </option>
                  <option value="JOIN_CIRCLE">🔵 Join Circle</option>
                  <option value="LEAVE_CHAT">🚪 Leave Chat</option>
                  <option value="INITIATE_CALL">📞 Initiate Call</option>
                  <option value="ANSWER_CALL">✅ Answer Call</option>
                  <option value="DECLINE_CALL">❌ Decline Call</option>
                  <option value="END_CALL">📴 End Call</option>
                  <option value="CALL_RINGING">🔔 Call Ringing</option>
                  <option value="MISSED_CALL">📞 Missed Call</option>
                  <option value="CALL_CANCELED">🚫 Call Canceled</option>
                  <option value="BUSY">⏳ Busy</option>
                  <option value="IGNORE_CALL">🔇 Ignore Call</option>
                  <option value="CHANGE_GROUP_TO_CIRCLE">
                    🔄 Change Group to Circle
                  </option>
                  <option value="TOGGLE_STATUS">🔄 Toggle Status</option>
                  <option value="CHAT_CALL">📞 Chat Call</option>
                  
                  <!-- Messages Gateway Events -->
                  <option value="CREATE_CIRCLE">🔵 Create Circle</option>
                  <option value="USER_DATA">👤 User Data</option>
                  <option value="CHAT_CALL_RESPONSE">📞 Chat Call Response</option>
                  <option value="NEW_USER">👤 New User</option>
                  <option value="REQUEST_CHANNELS">📡 Request Channels</option>
                  <option value="ADD_USERS_TO_CIRCLE">
                    👥 Add Users to Circle
                  </option>
                  <option value="LEAVE_CIRCLE">🚪 Leave Circle</option>
                  <option value="GET_PRIVATE_MESSAGES">
                    💬 Get Private Messages
                  </option>
                  <option value="GET_PRIVATE_MESSAGES_BY_PAGE">
                    📄 Get Private Messages (Paginated)
                  </option>
                  <option value="TYPING">⌨️ Typing</option>
                  <option value="DELETE_MESSAGE_ME">
                    🗑️ Delete Message (Me)
                  </option>
                  <option value="DELETE_MESSAGE_ALL">
                    🗑️ Delete Message (All)
                  </option>
                  <option value="GET_USER_MESSAGES">
                    � Get User Messages
                  </option>
                  <option value="ADD_USER_TO_CIRCLE">
                    �👥 Add User to Circle
                  </option>
                  <option value="ADD_REACTION">😀 Add Reaction</option>
                  <option value="REMOVE_REACTION">❌ Remove Reaction</option>
                  <option value="ADD_CONNECTION">🔗 Add Connection</option>
                  <option value="REQUEST_CONNECTIONS">
                    🔗 Request Connections
                  </option>
                  
                  <!-- Live Chat Events -->
                  <option value="START_CHAT">🟢 Start Chat</option>
                  <option value="END_CHAT">🔴 End Chat</option>
                  <option value="JOIN_CHAT">🚪 Join Chat</option>
                  <option value="UPGRADE_TO_TICKET">🎫 Upgrade to Ticket</option>
                  <option value="ADD_CHAT_REVIEW">⭐ Add Chat Review</option>
                  <option value="GET_CHAT_EXISTING_MESSAGES">
                    📬 Get Chat Existing Messages
                  </option>
                  <option value="GET_OPEN_CHATS">📂 Get Open Chats</option>
                  <option value="REJOIN_CHAT">🔄 Rejoin Chat</option>
                  <option value="NEW_CHAT">💬 New Chat</option>
                  <option value="GET_ACTIVE_CHATS">
                    🔥 Get Active Chats
                  </option>
                  
                  <!-- Events Gateway Events -->
                  <option value="JOIN_BACKSTAGE">🎭 Join Backstage</option>
                  <option value="GET_BACKSTAGE_MEMBERS">
                    👥 Get Backstage Members
                  </option>
                  <option value="NEW_BACKSTAGE_USER">
                    👤 New Backstage User
                  </option>
                  <option value="JOIN_MEETING">🎯 Join Meeting</option>
                </select>
              </div>

              <div>
                <label style="font-size: 1.1rem; margin-bottom: 10px"
                  >Event Channel</label
                >
                <select
                  id="eventChannel"
                  style="font-size: 16px; padding: 15px; height: auto"
                >
                  <option value="chat-event">💬 Chat Event</option>
                  <option value="message">📨 Message Event</option>
                  <option value="LIVE_CHAT_EVENT">🔴 Live Chat Event</option>
                  <option value="event">🎉 Event</option>
                </select>
                <div id="gatewayDisplay" style="margin-top: 8px; font-size: 0.9rem; color: #666; font-style: italic;">
                  Current: chat → chat-event
                </div>
              </div>
            </div>

            <div style="margin-bottom: 20px">
              <label
                style="font-size: 1.1rem; margin-bottom: 10px; display: block"
                >Event Data (JSON)</label
              >
              <div style="position: relative">
                <textarea
                  id="eventData"
                  class="json-editor"
                  rows="12"
                  style="font-size: 14px; line-height: 1.5; min-height: 300px"
                  placeholder='{\n  "userId": "test-user-123",\n  "tenantId": "tenant-456",\n  "data": {\n    "key": "value"\n  }\n}'
                ></textarea>
                <div
                  style="
                    position: absolute;
                    top: 10px;
                    right: 10px;
                    display: flex;
                    gap: 10px;
                  "
                >
                  <button
                    onclick="formatJSON()"
                    style="
                      background: #34495e;
                      color: white;
                      border: none;
                      padding: 5px 10px;
                      border-radius: 3px;
                      cursor: pointer;
                      font-size: 12px;
                    "
                  >
                    Format
                  </button>
                  <button
                    onclick="validateJSON()"
                    style="
                      background: #27ae60;
                      color: white;
                      border: none;
                      padding: 5px 10px;
                      border-radius: 3px;
                      cursor: pointer;
                      font-size: 12px;
                    "
                  >
                    Validate
                  </button>
                  <button
                    onclick="clearJSON()"
                    style="
                      background: #e74c3c;
                      color: white;
                      border: none;
                      padding: 5px 10px;
                      border-radius: 3px;
                      cursor: pointer;
                      font-size: 12px;
                    "
                  >
                    Clear
                  </button>
                </div>
              </div>
            </div>

            <div
              style="
                display: flex;
                gap: 15px;
                align-items: center;
                margin-bottom: 20px;
              "
            >
              <button
                class="btn btn-warning"
                onclick="sendCustomEvent()"
                style="font-size: 16px; padding: 15px 30px; flex: 1"
              >
                🚀 Send Custom Event
              </button>
              <button
                class="btn"
                onclick="testWithoutAuth()"
                style="font-size: 16px; padding: 15px 30px"
              >
                🧪 Test Mode
              </button>
              <button
                class="btn"
                onclick="loadTemplate()"
                style="font-size: 16px; padding: 15px 30px"
              >
                📋 Load Template
              </button>
              <button
                class="btn"
                onclick="saveTemplate()"
                style="font-size: 16px; padding: 15px 30px"
              >
                💾 Save Template
              </button>
            </div>

            <div
              style="
                background: #f8f9fa;
                padding: 15px;
                border-radius: 8px;
                border-left: 4px solid #3498db;
              "
            >
              <h4 style="margin: 0 0 10px 0; color: #2c3e50">💡 Quick Tips:</h4>
              <ul style="margin: 0; padding-left: 20px; color: #666">
                <li>Select gateway type to connect to the correct gateway</li>
                <li>Select event type to auto-populate template</li>
                <li>Use "Format" to beautify JSON</li>
                <li>Use "Validate" to check JSON syntax</li>
                <li>Use "Test Mode" for development without authentication</li>
                <li>Switch channels for different gateway types</li>
                <li>Save frequently used templates</li>
              </ul>
            </div>
          </div>

          <!-- Advanced Actions -->
          <div class="section">
            <h2>🔧 Advanced Actions</h2>
            <div class="quick-actions">
              <div class="action-card">
                <h3>� Message Actions</h3>
                <div class="btn-group">
                  <button class="btn" onclick="sendMessage()">Send</button>
                  <button class="btn" onclick="updateMessage()">Update</button>
                  <button class="btn btn-danger" onclick="deleteMessage()">
                    Delete
                  </button>
                  <button class="btn" onclick="pinMessage()">📌 Pin</button>
                  <button class="btn" onclick="addReaction()">� React</button>
                </div>
              </div>

              <div class="action-card">
                <h3>📞 Call Actions</h3>
                <div class="btn-group">
                  <button class="btn btn-success" onclick="initiateCall()">
                    📞 Initiate
                  </button>
                  <button class="btn btn-success" onclick="answerCall()">
                    ✅ Answer
                  </button>
                  <button class="btn btn-danger" onclick="declineCall()">
                    ❌ Decline
                  </button>
                  <button class="btn btn-warning" onclick="endCall()">
                    📴 End
                  </button>
                </div>
              </div>

              <div class="action-card">
                <h3>👥 Chat Management</h3>
                <div class="btn-group">
                  <button class="btn" onclick="createChat()">
                    ➕ Create Chat
                  </button>
                  <button class="btn" onclick="createCircle()">
                    🔵 Create Circle
                  </button>
                  <button class="btn" onclick="updateChat()">✏️ Update</button>
                  <button class="btn" onclick="addUsers()">� Add Users</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script>
      let socket = null;
      let isConnected = false;
      let autoScroll = true;
      let stats = { sent: 0, received: 0, errors: 0 };

      // Initialize the application
      function init() {
        logMessage('🚀 WebSocket test client initialized', 'info');
        updateStats();

        // Auto-fill some test data
        document.getElementById('chatId').value = 'chat-' + Date.now();
        document.getElementById('messageText').value =
          'Hello from test client!';

        // Setup event data template
        updateEventDataTemplate();
      }

      // Connection Management
      function connect() {
        if (socket && socket.connected) {
          disconnect();
        }

        const url = document.getElementById('serverUrl').value;
        const gatewayType = document.getElementById('gatewayType').value;
        const userId = document.getElementById('userId').value;
        const tenantId = document.getElementById('tenantId').value;
        const jobProUserId = document.getElementById('jobProUserId').value;
        const authToken = document.getElementById('authToken').value;
        const email = document.getElementById('email').value;

        const connectionOptions = {
          query: { userId, tenantId, jobProUserId, email },
        };

        if (authToken) {
          connectionOptions.auth = { authorization: `Bearer ${authToken}` };
          logMessage('🔐 Connecting with authentication', 'info');
        }

        // Connect to the default namespace (no namespace needed)
        socket = io(url, connectionOptions);

        logMessage(`🔄 Connecting to ${gatewayType} gateway...`, 'info');

        socket.on('connect', () => {
          isConnected = true;
          updateConnectionStatus(`🟢 Connected to ${gatewayType} gateway`, true);
          logMessage(`✅ Successfully connected to ${gatewayType} gateway`, 'info');
        });

        socket.on('disconnect', () => {
          isConnected = false;
          updateConnectionStatus('🔴 Disconnected', false);
          logMessage('❌ Disconnected from server', 'error');
        });

        // Chat Gateway events
        socket.on('chat-event', (data) => {
          stats.received++;
          updateStats();
          logMessage('📨 Chat event received', 'received', data);
        });

        // Messages Gateway events
        socket.on('message', (data) => {
          stats.received++;
          updateStats();
          
          // Enhanced handling for CREATE_CIRCLE events
          if (data.event === 'CREATE_CIRCLE') {
            if (data.error) {
              logMessage('❌ Circle creation failed', 'error', data);
            } else {
              logMessage('🎉 Circle created successfully', 'success', data);
              
              // Log key information about the created circle
              if (data.data && data.data.room) {
                logMessage(`📝 Circle Details: ${data.data.room.name} (ID: ${data.data.metadata?.roomId || 'N/A'})`, 'info');
                logMessage(`� Members: ${data.data.metadata?.memberCount || 0}`, 'info');
                logMessage(`🏢 Tenant: ${data.data.metadata?.tenantId || 'N/A'}`, 'info');
              }
            }
          } else {
            logMessage('�📩 Message event received', 'received', data);
          }
        });

        // Live Chat Gateway events
        socket.on('LIVE_CHAT_EVENT', (data) => {
          stats.received++;
          updateStats();
          logMessage('💬 Live chat event received', 'received', data);
        });

        // Events Gateway events
        socket.on('event', (data) => {
          stats.received++;
          updateStats();
          logMessage('🎉 Event received', 'received', data);
        });

        socket.on('connect_error', (error) => {
          stats.errors++;
          updateStats();
          logMessage('❌ Connection error', 'error', error);
        });
      }

      function disconnect() {
        if (socket) {
          socket.disconnect();
          socket = null;
          isConnected = false;
          updateConnectionStatus('🔴 Disconnected', false);
          logMessage('🔌 Manually disconnected', 'info');
        }
      }

      function updateConnectionStatus(status, connected) {
        const statusEl = document.getElementById('connectionStatus');
        statusEl.textContent = status;
        statusEl.className = `connection-status ${
          connected ? 'connected' : 'disconnected'
        }`;
      }

      // Helper function to get the correct event channel for the selected gateway type
      function getEventChannel() {
        const gatewayType = document.getElementById('gatewayType').value;
        switch (gatewayType) {
          case 'chat':
            return 'chat-event';
          case 'messages':
            return 'message';
          case 'live-chat':
            return 'LIVE_CHAT_EVENT';
          case 'events':
            return 'event';
          default:
            return 'chat-event';
        }
      }

      // Event Emission
      function emitEvent(channel, event, data) {
        if (!socket || !socket.connected) {
          logMessage('❌ Not connected to server', 'error');
          return false;
        }

        // If channel is not specified, use the appropriate channel for the current namespace
        if (!channel) {
          channel = getEventChannel();
        }

        const payload = { event, data };
        socket.emit(channel, payload);

        stats.sent++;
        updateStats();
        logMessage(`📤 Sent ${event} on ${channel}`, 'sent', payload);
        return true;
      }

      // Quick Test Functions
      function getUserData() {
        const data = {
          userId: document.getElementById('userId').value,
          tenantId: document.getElementById('tenantId').value,
          jobProUserId: document.getElementById('jobProUserId').value,
          email: document.getElementById('email').value,
        };
        emitEvent(null, 'GET_USER_DATA', data);
      }

      function getChats() {
        const data = {
          userId: document.getElementById('userId').value,
          tenantId: document.getElementById('tenantId').value,
          jobProUserId: document.getElementById('jobProUserId').value,
        };
        emitEvent(null, 'GET_CHATS_WITH_UNREAD_COUNT', data);
      }

      function createTestChat() {
        const data = {
          name: 'Test Chat ' + new Date().toLocaleTimeString(),
          createdBy: document.getElementById('userId').value,
          users: [document.getElementById('userId').value],
          jobProUserIds: [document.getElementById('jobProUserId').value],
          tenantId: document.getElementById('tenantId').value,
          chatType: 'CHAT',
          isPrivate: false,
        };
        emitEvent(null, 'CREATE_CHAT', data);
      }

      // Test Mode function for development/testing without auth
      function testWithoutAuth() {
        const eventType = document.getElementById('eventType').value;
        const eventDataText = document.getElementById('eventData').value;
        
        let eventData;
        try {
          eventData = eventDataText ? JSON.parse(eventDataText) : {};
        } catch (e) {
          logMessage('❌ Invalid JSON in Event Data: ' + e.message, 'error');
          return;
        }

        if (!socket || !socket.connected) {
          logMessage('❌ Not connected to server', 'error');
          return;
        }

        const payload = { event: eventType, data: eventData };
        socket.emit('test-event', payload);

        stats.sent++;
        updateStats();
        logMessage(`📤 Sent test event ${eventType}`, 'sent', payload);
      }

      function sendTestMessage() {
        const chatId = document.getElementById('chatId').value;
        const messageText = document.getElementById('messageText').value;

        if (!chatId || !messageText) {
          logMessage('❌ Please enter Chat ID and Message', 'error');
          return;
        }

        const data = {
          text: messageText,
          chatId: chatId,
          sender: document.getElementById('userId').value,
          messageType: 'text',
        };
        emitEvent('chat-event', 'NEW_MESSAGE', data);
      }

      function startTypingTest() {
        const chatId = document.getElementById('chatId').value;
        if (!chatId) {
          logMessage('❌ Please enter Chat ID', 'error');
          return;
        }

        const data = {
          chatId: chatId,
          userId: document.getElementById('userId').value,
          isTyping: true,
        };
        emitEvent('chat-event', 'IS_TYPING', data);

        // Stop typing after 3 seconds
        setTimeout(() => {
          data.isTyping = false;
          emitEvent('chat-event', 'IS_TYPING', data);
        }, 3000);
      }

      function testCall() {
        const chatId = document.getElementById('chatId').value;
        if (!chatId) {
          logMessage('❌ Please enter Chat ID', 'error');
          return;
        }

        const data = {
          chatId: chatId,
          userId: document.getElementById('userId').value,
          callType: 'video',
          callerId: document.getElementById('userId').value,
          callerName: 'Test User',
          meetingId: chatId,
          format: 'video',
          type: 'private',
          callId: 'call-' + Date.now(),
        };
        emitEvent('chat-event', 'INITIATE_CALL', data);
      }

      // Message Actions
      function sendMessage() {
        sendTestMessage();
      }

      function updateMessage() {
        const chatId = document.getElementById('chatId').value;
        const messageId = document.getElementById('messageId').value;
        const messageText = document.getElementById('messageText').value;

        if (!chatId || !messageId || !messageText) {
          logMessage(
            '❌ Please enter Chat ID, Message ID, and Message',
            'error',
          );
          return;
        }

        const data = {
          messageId: messageId,
          text: messageText,
          chatId: chatId,
          userId: document.getElementById('userId').value,
        };
        emitEvent('chat-event', 'UPDATE_MESSAGE', data);
      }

      function deleteMessage() {
        const chatId = document.getElementById('chatId').value;
        const messageId = document.getElementById('messageId').value;

        if (!chatId || !messageId) {
          logMessage('❌ Please enter Chat ID and Message ID', 'error');
          return;
        }

        const data = {
          messageId: messageId,
          chatId: chatId,
          userId: document.getElementById('userId').value,
        };
        emitEvent('chat-event', 'DELETE_MESSAGE', data);
      }

      function pinMessage() {
        const chatId = document.getElementById('chatId').value;
        const messageId = document.getElementById('messageId').value;

        if (!chatId || !messageId) {
          logMessage('❌ Please enter Chat ID and Message ID', 'error');
          return;
        }

        const data = {
          messageId: messageId,
          isPinned: true,
          pinnedBy: document.getElementById('userId').value,
          chatId: chatId,
        };
        emitEvent('chat-event', 'PIN_UNPIN_MESSAGE', data);
      }

      function addReaction() {
        const chatId = document.getElementById('chatId').value;
        const messageId = document.getElementById('messageId').value;

        if (!chatId || !messageId) {
          logMessage('❌ Please enter Chat ID and Message ID', 'error');
          return;
        }

        const data = {
          messageId: messageId,
          userId: document.getElementById('userId').value,
          reaction: '👍',
          chatId: chatId,
        };
        emitEvent('chat-event', 'ADD_REACTION_TO_MESSAGE', data);
      }

      // Call Actions
      function initiateCall() {
        testCall();
      }

      function answerCall() {
        const chatId = document.getElementById('chatId').value;
        if (!chatId) {
          logMessage('❌ Please enter Chat ID', 'error');
          return;
        }

        const data = {
          chatId: chatId,
          userId: document.getElementById('userId').value,
          callId: 'call-' + Date.now(),
          callerId: document.getElementById('userId').value,
          format: 'video',
          type: 'private',
        };
        emitEvent('chat-event', 'ANSWER_CALL', data);
      }

      function declineCall() {
        const chatId = document.getElementById('chatId').value;
        if (!chatId) {
          logMessage('❌ Please enter Chat ID', 'error');
          return;
        }

        const data = {
          chatId: chatId,
          userId: document.getElementById('userId').value,
          callId: 'call-' + Date.now(),
          status: 'declined',
        };
        emitEvent('chat-event', 'DECLINE_CALL', data);
      }

      function endCall() {
        const chatId = document.getElementById('chatId').value;
        if (!chatId) {
          logMessage('❌ Please enter Chat ID', 'error');
          return;
        }

        const data = {
          chatId: chatId,
          userId: document.getElementById('userId').value,
          callId: 'call-' + Date.now(),
          endTime: Date.now(),
        };
        emitEvent('chat-event', 'END_CALL', data);
      }

      // Chat Management
      function createChat() {
        createTestChat();
      }

      function createCircle() {
        const userId = document.getElementById('userId').value;
        const jobProUserId = document.getElementById('jobProUserId').value;
        const tenantId = document.getElementById('tenantId').value;
        
        const data = {
          user: {
            id: userId,
            jobProUserId: jobProUserId,
            tenantId: tenantId,
            username: 'Test User ' + userId.slice(-4),
            email: '<EMAIL>',
          },
          room: {
            name: 'Test Circle ' + new Date().toLocaleTimeString(),
            description: 'Test circle created via socket test client',
            tenantId: tenantId,
            isArchived: false,
            subdomain: 'localhost',
          },
          members: [
            {
              id: userId,
              jobProUserId: jobProUserId,
              username: 'Test User ' + userId.slice(-4),
              email: '<EMAIL>',
              role: 'admin',
            },
            // Add additional test members if needed
            {
              id: 'member-2',
              jobProUserId: 'jpuser-2',
              username: 'Test Member 2',
              email: '<EMAIL>',
              role: 'member',
            }
          ],
          subdomain: 'localhost',
        };
        emitEvent('message', 'CREATE_CIRCLE', data);
      }

      function updateChat() {
        const chatId = document.getElementById('chatId').value;
        if (!chatId) {
          logMessage('❌ Please enter Chat ID', 'error');
          return;
        }

        const data = {
          chatId: chatId,
          userId: document.getElementById('userId').value,
          update: {
            name: 'Updated Chat ' + new Date().toLocaleTimeString(),
            description: 'Updated via test client',
          },
        };
        emitEvent('chat-event', 'UPDATE_CHAT', data);
      }

      function addUsers() {
        const chatId = document.getElementById('chatId').value;
        if (!chatId) {
          logMessage('❌ Please enter Chat ID', 'error');
          return;
        }

        const data = {
          chatId: chatId,
          userIds: ['new-user-' + Date.now()],
          jobProUserIds: ['jobpro-' + Date.now()],
        };
        emitEvent('chat-event', 'ADD_USERS_TO_CHAT', data);
      }

      // Custom Event
      function sendCustomEvent() {
        const eventType = document.getElementById('eventType').value;
        const eventChannel = document.getElementById('eventChannel').value;
        const eventDataText = document.getElementById('eventData').value;

        let eventData;
        try {
          eventData = eventDataText ? JSON.parse(eventDataText) : {};
        } catch (e) {
          logMessage('❌ Invalid JSON in Event Data: ' + e.message, 'error');
          return;
        }

        emitEvent(eventChannel, eventType, eventData);
      }

      function updateEventDataTemplate() {
        const eventType = document.getElementById('eventType').value;
        const userId = document.getElementById('userId').value;
        const tenantId = document.getElementById('tenantId').value;
        const jobProUserId = document.getElementById('jobProUserId').value;
        const chatId = document.getElementById('chatId').value;
        const gatewayType = document.getElementById('gatewayType').value;

        let template = {};
        let channel = getEventChannel(); // Use the gateway type-aware function

        switch (eventType) {
          case 'GET_USER_DATA':
            template = { userId, tenantId, jobProUserId };
            break;
          case 'GET_CHATS_WITH_UNREAD_COUNT':
            template = { userId, tenantId, jobProUserId };
            break;
          case 'GET_CHAT_PAGINATED_MESSAGES':
            template = {
              chatId,
              userId,
              page: 1,
              limit: 20,
              sortBy: 'createdAt',
              sortOrder: 'desc',
            };
            break;
          case 'CREATE_CHAT':
            template = {
              name: 'New Chat',
              createdBy: userId,
              users: [userId],
              jobProUserIds: [jobProUserId],
              tenantId: tenantId,
              chatType: 'CHAT',
              isPrivate: false,
            };
            break;
          case 'CREATE_CIRCLE':
            template = {
              user: {
                id: userId,
                jobProUserId: jobProUserId,
                tenantId: tenantId,
                username: 'Test User ' + userId.slice(-4),
                email: '<EMAIL>',
              },
              room: {
                name: 'Template Circle ' + new Date().toLocaleTimeString(),
                description: 'Circle created via template',
                tenantId: tenantId,
                isArchived: false,
                subdomain: 'localhost',
              },
              members: [
                {
                  id: userId,
                  jobProUserId: jobProUserId,
                  username: 'Test User ' + userId.slice(-4),
                  email: '<EMAIL>',
                  role: 'admin',
                },
              ],
              subdomain: 'localhost',
            };
            // CREATE_CIRCLE should only be available in messages gateway
            channel = 'message';
            break;
          case 'MARK_ALL_AS_READ':
            template = {
              chatId,
              userId,
            };
            break;
          case 'START_CHAT':
            template = {
              firstName: 'Test',
              lastName: 'User',
              profilePicture: 'https://example.com/profile.jpg',
            };
            channel = 'LIVE_CHAT_EVENT';
            break;
          case 'END_CHAT':
            template = {
              chatId: 'chat-id-here',
            };
            channel = 'LIVE_CHAT_EVENT';
            break;
          case 'JOIN_CHAT':
            template = {
              chatId: 'chat-id-here',
              firstName: 'Test',
              lastName: 'User',
              profilePicture: 'https://example.com/profile.jpg',
            };
            channel = 'LIVE_CHAT_EVENT';
            break;
          case 'GET_CHAT_EXISTING_MESSAGES':
            template = {
              chatId: 'chat-id-here',
            };
            channel = 'LIVE_CHAT_EVENT';
            break;
          case 'GET_OPEN_CHATS':
            template = {};
            channel = 'LIVE_CHAT_EVENT';
            break;
          case 'GET_ACTIVE_CHATS':
            template = {};
            channel = 'LIVE_CHAT_EVENT';
            break;
          case 'JOIN_BACKSTAGE':
            template = {
              userId,
              tenantId,
              backstageId: 'backstage-id-here',
            };
            channel = 'event';
            break;
          case 'GET_BACKSTAGE_MEMBERS':
            template = {
              backstageId: 'backstage-id-here',
            };
            channel = 'event';
            break;
          case 'JOIN_MEETING':
            template = {
              meetingId: 'meeting-id-here',
              userId,
            };
            channel = 'event';
            break;
          case 'NEW_MESSAGE':
            template = {
              text: 'Hello from custom event!',
              chatId,
              sender: userId,
              messageType: 'text',
              mentions: [],
              reactions: [],
              isPinned: false,
              isDeleted: false,
              readBy: [userId],
            };
            break;
          case 'UPDATE_MESSAGE':
            template = {
              messageId: 'message-id-here',
              text: 'Updated message text',
              chatId,
              userId,
            };
            break;
          case 'DELETE_MESSAGE':
            template = {
              messageId: 'message-id-here',
              chatId,
              userId,
            };
            break;
          case 'PIN_UNPIN_MESSAGE':
            template = {
              messageId: 'message-id-here',
              isPinned: true,
              pinnedBy: userId,
              chatId,
            };
            break;
          case 'ADD_REACTION_TO_MESSAGE':
            template = {
              messageId: 'message-id-here',
              userId,
              reaction: '👍',
              chatId,
            };
            break;
          case 'REMOVE_REACTION_FROM_MESSAGE':
            template = {
              messageId: 'message-id-here',
              userId,
              reaction: '👍',
              chatId,
            };
            break;
          case 'IS_TYPING':
            template = { chatId, userId, isTyping: true };
            break;
          case 'IS_RECORDING':
            template = {
              chatId,
              userId,
              isRecording: true,
              message: 'User is recording',
            };
            break;
          case 'UPDATE_READ_STATUS':
            template = { chatId, userId, messageIds: ['msg-1', 'msg-2'] };
            break;
          case 'GET_ALL_UNREAD_MESSAGES':
            template = { userId, tenantId };
            break;
          case 'INITIATE_CALL':
            template = {
              chatId,
              userId,
              callType: 'video',
              callerId: userId,
              callerName: 'Test User',
              meetingId: chatId,
              format: 'video',
              type: 'private',
              callId: 'call-' + Date.now(),
            };
            break;
          case 'ANSWER_CALL':
            template = {
              chatId,
              userId,
              callId: 'call-id-here',
              callerId: userId,
              format: 'video',
              type: 'private',
            };
            break;
          case 'DECLINE_CALL':
            template = {
              chatId,
              userId,
              callId: 'call-id-here',
              status: 'declined',
              reason: 'User declined',
            };
            break;
          case 'END_CALL':
            template = {
              chatId,
              userId,
              callId: 'call-id-here',
              endTime: Date.now(),
              callDuration: 120,
            };
            break;
          case 'CALL_RINGING':
            template = {
              chatId,
              userId,
              callId: 'call-id-here',
              callerId: userId,
              status: 'ringing',
            };
            break;
          case 'MISSED_CALL':
            template = {
              chatId,
              userId,
              callId: 'call-id-here',
              callerId: userId,
              status: 'missed',
            };
            break;
          case 'CALL_CANCELED':
            template = {
              chatId,
              userId,
              callId: 'call-id-here',
              status: 'cancelled',
            };
            break;
          case 'BUSY':
            template = {
              chatId,
              userId,
              callerId: userId,
              message: 'User is busy',
            };
            break;
          case 'IGNORE_CALL':
            template = {
              chatId,
              userId,
              callId: 'call-id-here',
              callerId: userId,
            };
            break;
          case 'UPDATE_CHAT':
            template = {
              chatId,
              userId,
              update: {
                name: 'Updated Chat Name',
                description: 'Updated description',
              },
            };
            break;
          case 'ADD_USERS_TO_CHAT':
            template = {
              chatId,
              userIds: ['new-user-id'],
              jobProUserIds: ['new-jobpro-id'],
            };
            break;
          case 'REMOVE_USER_FROM_CHAT':
            template = {
              chatId,
              userId: 'user-to-remove',
              jobProUserId: 'jobpro-to-remove',
            };
            break;
          default:
            template = { userId, tenantId, jobProUserId };
        }

        document.getElementById('eventData').value = JSON.stringify(
          template,
          null,
          2,
        );
        document.getElementById('eventChannel').value = channel;
        
        // Update gateway display
        const gatewayDisplay = document.getElementById('gatewayDisplay');
        if (gatewayDisplay) {
          gatewayDisplay.textContent = `Current: ${gatewayType} → ${channel}`;
        }
      }

      function formatJSON() {
        const textarea = document.getElementById('eventData');
        try {
          const parsed = JSON.parse(textarea.value);
          textarea.value = JSON.stringify(parsed, null, 2);
          logMessage('✅ JSON formatted successfully', 'info');
        } catch (e) {
          logMessage('❌ Invalid JSON format: ' + e.message, 'error');
        }
      }

      function validateJSON() {
        const textarea = document.getElementById('eventData');
        try {
          JSON.parse(textarea.value);
          logMessage('✅ JSON is valid', 'info');
        } catch (e) {
          logMessage('❌ Invalid JSON: ' + e.message, 'error');
        }
      }

      function clearJSON() {
        document.getElementById('eventData').value = '';
        logMessage('🧹 JSON cleared', 'info');
      }

      function loadTemplate() {
        updateEventDataTemplate();
        logMessage(
          '📋 Template loaded for ' +
            document.getElementById('eventType').value,
          'info',
        );
      }

      function saveTemplate() {
        const eventType = document.getElementById('eventType').value;
        const eventData = document.getElementById('eventData').value;

        try {
          JSON.parse(eventData); // Validate JSON
          localStorage.setItem(`websocket-template-${eventType}`, eventData);
          logMessage('💾 Template saved for ' + eventType, 'info');
        } catch (e) {
          logMessage('❌ Cannot save invalid JSON', 'error');
        }
      }

      function loadSavedTemplate() {
        const eventType = document.getElementById('eventType').value;
        const saved = localStorage.getItem(`websocket-template-${eventType}`);

        if (saved) {
          document.getElementById('eventData').value = saved;
          logMessage('📋 Loaded saved template for ' + eventType, 'info');
        } else {
          updateEventDataTemplate();
        }
      }

      // Utility Functions
      function updateStats() {
        document.getElementById('sentCount').textContent = stats.sent;
        document.getElementById('receivedCount').textContent = stats.received;
        document.getElementById('errorCount').textContent = stats.errors;
      }

      function logMessage(message, type, data = null) {
        const logContainer = document.getElementById('logContainer');
        const timestamp = new Date().toLocaleTimeString();

        const logEntry = document.createElement('div');
        logEntry.className = `log-entry log-${type}`;

        let content = `<span class="timestamp">[${timestamp}]</span> ${message}`;
        if (data) {
          content += `<div class="log-content"><pre>${JSON.stringify(
            data,
            null,
            2,
          )}</pre></div>`;
        }

        logEntry.innerHTML = content;
        logContainer.appendChild(logEntry);

        if (autoScroll) {
          logContainer.scrollTop = logContainer.scrollHeight;
        }
      }

      function clearLog() {
        document.getElementById('logContainer').innerHTML = '';
        stats = { sent: 0, received: 0, errors: 0 };
        updateStats();
        logMessage('🧹 Log cleared', 'info');
      }

      function toggleAutoScroll() {
        autoScroll = !autoScroll;
        document.getElementById(
          'autoScrollText',
        ).textContent = `🔄 Auto-scroll: ${autoScroll ? 'ON' : 'OFF'}`;
      }

      function exportLog() {
        const logContainer = document.getElementById('logContainer');
        const logText = logContainer.innerText;

        const blob = new Blob([logText], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `websocket-log-${new Date()
          .toISOString()
          .slice(0, 19)}.txt`;
        a.click();
        URL.revokeObjectURL(url);
      }

      // Event Listeners
      document
        .getElementById('eventType')
        .addEventListener('change', loadSavedTemplate);

      // Initialize on page load
      window.addEventListener('load', init);
    </script>
  </body>
</html>
