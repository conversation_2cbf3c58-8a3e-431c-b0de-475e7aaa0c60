/* eslint-disable @typescript-eslint/no-var-requires */
/* eslint-disable prettier/prettier */
/*
https://docs.nestjs.com/providers#services
*/

import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Response } from 'express';
import { FileDto } from 'src/messages/dto/file.dto';
import { LogService } from 'src/log/log.service';
import { AwsService } from './awsservice';
// const random = (() => {
//     const buf = Buffer.alloc(16);
//     return () => randomFillSync(buf).toString('hex');
// })();

@Injectable()
export class FileService {

    constructor(
        private configService: ConfigService,
        private logService: LogService,
        private awsService: AwsService
    ) {
    }

    async getS3FilePublicUrl(filename: string) {
        return await this.awsService.getS3FilePublicUrl(filename);
    }
    getFromS3(filename: string, res: Response) {
        return this.awsService.getFromS3(filename, res);
    }
    async s3UploadWithResumeable(file: Express.Multer.File) {
        // This method can be implemented later for resumable uploads if needed
        // For now, delegate to the standard upload method
        return await this.s3Upload(file);
    }

    async s3Upload(file: Express.Multer.File) {
        return await this.awsService.s3Upload(file);
    }

    /**
     * Delete a file from S3
     * @param filename - The filename/key in S3
     * @returns Promise<boolean> - Success status
     */
    async deleteFromS3(filename: string): Promise<boolean> {
        return await this.awsService.deleteFromS3(filename);
    }

    /**
     * Check S3 connection status
     * @returns Promise<boolean> - Connection status
     */
    async checkS3Connection(): Promise<boolean> {
        return await this.awsService.checkS3Connection();
    }

    // Legacy method names for backward compatibility
    async gcsUpload(file: Express.Multer.File) {
        this.logService.log('Using legacy gcsUpload method, redirecting to S3');
        return await this.s3Upload(file);
    }

    async getGCSFilePublicUrl(filename: string) {
        this.logService.log('Using legacy getGCSFilePublicUrl method, redirecting to S3');
        return await this.getS3FilePublicUrl(filename);
    }

    getFromGCS(filename: string, res: Response) {
        this.logService.log('Using legacy getFromGCS method, redirecting to S3');
        return this.getFromS3(filename, res);
    }
}
